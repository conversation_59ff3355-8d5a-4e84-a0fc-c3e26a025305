package com.sanythadmin.project.score.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 体育成绩查询参数
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScoreTycjParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 学号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 测评年份
     */
    private String cpnf;


    /**
     * 姓名
     */
    private String xm;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.EQ)
    private String xyid;

    /**
     * 专业ID
     */
    @QueryField(type = QueryType.EQ)
    private String zyid;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.EQ)
    private String bjid;

    /**
     * 参数配置ID
     */
    @QueryField(type = QueryType.EQ)
    private String configId;
}
