package com.sanythadmin.project.score.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.ScoreMycj;
import com.sanythadmin.project.score.param.ScoreMycjParam;

import java.util.List;
import java.util.Map;

/**
 * 美育成绩Service
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
public interface ScoreMycjService extends IService<ScoreMycj> {
    public PageResult<ScoreMycj> page(ScoreMycjParam param);

    public void saveScore(ScoreMycj object);

    public void removeByParam(ScoreMycjParam param);

    public List<ScoreMycj> list(ScoreMycjParam param);
    public List<Map<String, Object>> years();
}
