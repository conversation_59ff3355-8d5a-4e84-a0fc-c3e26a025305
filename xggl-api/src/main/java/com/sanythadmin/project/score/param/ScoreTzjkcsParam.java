package com.sanythadmin.project.score.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 体质健康测试成绩查询参数
 *
 * <AUTHOR>
 * @since 2025-07-27 09:47:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScoreTzjkcsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 测评年份
     */
    private String cpnf;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 学院ID
     */
    private String xyid;

    /**
     * 专业ID
     */
    @QueryField(type = QueryType.EQ)
    private String zyid;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.EQ)
    private String bjid;

    private String createTime;

    @QueryField(type = QueryType.EQ)
    private String njid;

    @QueryField(type = QueryType.EQ)
    private String pyccid;

    private String configId;

}
