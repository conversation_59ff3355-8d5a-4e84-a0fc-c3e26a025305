package com.sanythadmin.project.score.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.ScoreMycj;
import com.sanythadmin.project.score.entity.ScoreTycj;
import com.sanythadmin.project.score.mapper.ScoreTycjMapper;
import com.sanythadmin.project.score.param.ScoreTycjParam;
import com.sanythadmin.project.score.service.ScoreTycjService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 体育成绩Service实现
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
@Service
public class ScoreTycjServiceImpl extends ServiceImpl<ScoreTycjMapper, ScoreTycj> implements ScoreTycjService {

    @Resource
    private ScoreTycjMapper mapper;

    @Override
    public PageResult<ScoreTycj> page(ScoreTycjParam param) {
        PageParam<ScoreTycj, ScoreTycjParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = mapper.selectPageWithPermission(page, page.getWrapper(), new MyMPQueryParams(ScoreTycj.class));
        List<ScoreTycj> records = page.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void saveScore(ScoreTycj object) {
        mapper.delete(new LambdaQueryWrapper<ScoreTycj>()
                .eq(ScoreTycj::getXgh, object.getXgh())
                .eq(ScoreTycj::getCpnf, object.getCpnf()));
        mapper.insert(object);
    }

    @Transactional
    @Override
    public void removeByParam(ScoreTycjParam param) {
        PageParam<ScoreTycj, ScoreTycjParam> page = new PageParam<>(param);
        mapper.delete(page.getWrapper());
    }

    @Override
    public List<ScoreTycj> list(ScoreTycjParam param) {
        PageParam<ScoreTycj, ScoreTycjParam> page = new PageParam<>(param);
        return mapper.selectListWithPermission(page.getWrapper(), new MyMPQueryParams(ScoreTycj.class));
    }

    @Override
    public List<Map<String, Object>> years() {
        List<Map<String, Object>> maps = baseMapper.selectMaps(new LambdaQueryWrapper<ScoreTycj>()
                .select(ScoreTycj::getCpnf)
                .groupBy(ScoreTycj::getCpnf)
                .orderByDesc(ScoreTycj::getCpnf));
        List<Map<String, Object>> newMaps = new ArrayList<>();
        if (!CollectionUtils.isEmpty(maps)) {
            for (Map<String, Object> map : maps) {
                Map<String, Object> newMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    newMap.put(entry.getKey().toLowerCase(), entry.getValue());
                }
                newMaps.add(newMap);
            }
        }
        return newMaps;
    }
}
