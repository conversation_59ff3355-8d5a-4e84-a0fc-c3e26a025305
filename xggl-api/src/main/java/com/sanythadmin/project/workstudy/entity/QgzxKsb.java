package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.workstudy.enums.WeekDay;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 勤工助学课时表(手工维护/教务同步)
 *
 * <AUTHOR>
 * @since 2025-07-10 15:33:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_KSB")
@Entity
@Table(name = "SYT_QGZX_KSB")
public class QgzxKsb implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ")
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 开始时间
     */
    @Column(name = "KSSJ")
    @TableField("KSSJ")
    private String kssj;

    /**
     * 结束时间
     */
    @Column(name = "JSSJ")
    @TableField("JSSJ")
    private String jssj;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 1-7表示周一到周日
     */
    @Column(name = "WEEK_DAY")
    @TableField("WEEK_DAY")
    private WeekDay weekDay;
    

}
