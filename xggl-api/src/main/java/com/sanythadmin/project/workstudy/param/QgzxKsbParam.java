package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.workstudy.enums.TimeType;
import com.sanythadmin.project.workstudy.enums.WeekDay;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 勤工助学课时表查询参数
 *
 * <AUTHOR>
 * @since 2025-07-10 15:33:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxKsbParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学年学期
     */
    private String xnxq;

    /**
     * 名称
     */
    private String name;

    /**
     * 开始时间
     */
    private String kssj;

    /**
     * 结束时间
     */
    private String jssj;

    /**
     * 1-7表示周一到周日
     */
    @QueryField(type = QueryType.EQ)
    private WeekDay weekDay;

    /**
     * 时间段类型
     */
    @QueryField(type = QueryType.EQ)
    private TimeType timeType;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }

}
