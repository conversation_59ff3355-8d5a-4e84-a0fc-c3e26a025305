-- 为勤工助学课时表添加时间段类型字段
-- <AUTHOR>
-- @since 2025-07-28

-- 1. 添加时间段类型字段
ALTER TABLE SYT_QGZX_KSB ADD COLUMN TIME_TYPE VARCHAR2(20);

-- 2. 添加字段注释
COMMENT ON COLUMN SYT_QGZX_KSB.TIME_TYPE IS '时间段类型：COURSE-课表时间，FREE-空闲时间';

-- 3. 为现有数据设置默认值（默认为课表时间）
UPDATE SYT_QGZX_KSB SET TIME_TYPE = 'COURSE' WHERE TIME_TYPE IS NULL;

-- 4. 设置字段为非空
ALTER TABLE SYT_QGZX_KSB MODIFY TIME_TYPE VARCHAR2(20) NOT NULL;

-- 5. 添加检查约束，确保只能是COURSE或FREE
ALTER TABLE SYT_QGZX_KSB ADD CONSTRAINT CHK_QGZX_KSB_TIME_TYPE 
CHECK (TIME_TYPE IN ('COURSE', 'FREE'));

-- 6. 创建索引以提高查询性能
CREATE INDEX IDX_QGZX_KSB_TIME_TYPE ON SYT_QGZX_KSB(TIME_TYPE);

-- 7. 创建复合索引，优化按学年学期和时间段类型查询
CREATE INDEX IDX_QGZX_KSB_XNXQ_TIME_TYPE ON SYT_QGZX_KSB(XNXQ, TIME_TYPE);
