package com.sanythadmin.project.workstudy.service;

import com.sanythadmin.project.workstudy.entity.QgzxKsb;
import com.sanythadmin.project.workstudy.enums.TimeType;
import com.sanythadmin.project.workstudy.enums.WeekDay;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

/**
 * 勤工助学课时表Service测试
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@SpringBootTest
@ActiveProfiles("test")
public class QgzxKsbServiceTest {

    @Resource
    private QgzxKsbService qgzxKsbService;

    @Test
    public void testCreateKsb() {
        // 创建课表时间
        QgzxKsb courseTime = new QgzxKsb();
        courseTime.setXnxq("2024-2025-1");
        courseTime.setName("第1-2节课");
        courseTime.setKssj("08:00");
        courseTime.setJssj("09:40");
        courseTime.setWeekDay(WeekDay.MONDAY);
        courseTime.setTimeType(TimeType.COURSE);
        courseTime.setSort(1);
        
        qgzxKsbService.save(courseTime);
        System.out.println("创建课表时间成功：" + courseTime.getId());

        // 创建空闲时间
        QgzxKsb freeTime = new QgzxKsb();
        freeTime.setXnxq("2024-2025-1");
        freeTime.setName("午休时间");
        freeTime.setKssj("12:00");
        freeTime.setJssj("14:00");
        freeTime.setWeekDay(WeekDay.MONDAY);
        freeTime.setTimeType(TimeType.FREE);
        freeTime.setSort(2);
        
        qgzxKsbService.save(freeTime);
        System.out.println("创建空闲时间成功：" + freeTime.getId());
    }

    @Test
    public void testQueryByTimeType() {
        String xnxq = "2024-2025-1";
        
        // 查询课表时间
        List<QgzxKsb> courseList = qgzxKsbService.getByXnxqAndTimeType(xnxq, TimeType.COURSE);
        System.out.println("课表时间数量：" + courseList.size());
        
        // 查询空闲时间
        List<QgzxKsb> freeList = qgzxKsbService.getByXnxqAndTimeType(xnxq, TimeType.FREE);
        System.out.println("空闲时间数量：" + freeList.size());
        
        // 查询所有时间
        List<QgzxKsb> allList = qgzxKsbService.getByXnxqAndTimeType(xnxq, null);
        System.out.println("总时间段数量：" + allList.size());
    }
}
