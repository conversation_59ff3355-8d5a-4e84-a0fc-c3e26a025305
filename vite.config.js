import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import Compression from 'vite-plugin-compression';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import { EleAdminResolver } from 'ele-admin-plus/es/utils/resolvers';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import path from 'path';

export default defineConfig(({ command }) => {
  const isBuild = command === 'build';
  const alias = {
    '@/': resolve('src') + '/',
    'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js'
  };
  // langJsx必须在 vue 插件前
  const plugins = [
    vue(),
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), 'src/icons/svg')],
      symbolId: '[name]'
    }),
    Components({
      dts: false,
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass'
        }),
        EleAdminResolver({
          importStyle: 'sass'
        })
      ]
    })
  ];

  if (isBuild) {
    plugins.push(
      Compression({
        disable: !isBuild,
        threshold: 10240,
        algorithm: 'gzip',
        ext: '.gz'
      })
    );
  }
  // const plugins = [
  //   vue(),
  //   createSvgIconsPlugin({
  //     // 指定需要缓存的图标文件夹
  //     iconDirs: [path.resolve(process.cwd(), 'src/icons/svg')],
  //     // 指定symbolId格式
  //     symbolId: '[name]'
  //   })
  // ];
  // if (isBuild) {
  //   // 组件按需引入
  //   plugins.push(
  //     Components({
  //       dts: false,
  //       resolvers: [
  //         ElementPlusResolver({
  //           importStyle: 'sass'
  //         }),
  //         EleAdminResolver({
  //           importStyle: 'sass'
  //         })
  //       ]
  //     })
  //   );
  //   // gzip压缩
  //   plugins.push(
  //     Compression({
  //       disable: !isBuild,
  //       threshold: 10240,
  //       algorithm: 'gzip',
  //       ext: '.gz'
  //     })
  //   );
  //   // } else {
  //   //   // 开发环境全局安装
  //   //   alias['./as-needed'] = './global-import';
  // }
  return {
    server: {
      host: '0.0.0.0',
      // port: 8088,  // 这里写要改的端口
      proxy: {
        // '/api': 'http://*************:8082/'
        '/api': 'http://*************:8082/' //演示环境
        // '/api': 'http://************:8081/', //洪川
        // '/api': 'http://************:9018'
        // '/api': 'http://*************:7081/'
        // '/api': 'http://************:8082/'//zwb
        // '/api': 'http://***************:8001/'
        // '/api': 'http://*************:8082/'
        // 注意, 这种方式需要接口都有一个统一的前缀, 这里 /api 就是所有接口都会有的前缀
        // 如果接口没有统一前缀可以这样写最后把 /api 去掉
      }
    },
    resolve: {
      alias
    },
    plugins,

    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`
        }
      }
    },
    optimizeDeps: {
      include: [
        'echarts-wordcloud',
        'vuedraggable',
        'sortablejs',
        'xlsx',
        'echarts',
        'echarts-gl',
        'echarts/core',
        'echarts/charts',
        'echarts/renderers',
        'echarts/components',
        'echarts-gl/src/component/map3D/Map3D',
        'vue-echarts'
      ]
    },
    build: {
      target: 'es2020',
      chunkSizeWarningLimit: 2000
    }
  };
});
