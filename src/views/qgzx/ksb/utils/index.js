import {transformDicDataName} from '@/utils/common_bak2.js';

export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    },
    {
      prop: 'xnxq',
      label: '学年学期',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'name',
      label: '名称',
      slot: 'name',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'kssj',
      label: '开始时间',
      type: 'time',
      valueFormat: 'hh:mm',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'jssj',
      label: '结束时间',
      type: 'time',
      valueFormat: 'hh:mm',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'weekDay',
      label: '星期',
      type: 'select',
      typeKey: 'select',
      props: {
        options: [
          { value: 1, label: '星期一' },
          { value: 2, label: '星期二' },
          { value: 3, label: '星期三' },
          { value: 4, label: '星期四' },
          { value: 5, label: '星期五' },
          { value: 6, label: '星期六' },
          { value: 7, label: '星期日' }
        ]
      },
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      formatter: (row) => {
        const weekDayMap = {
          1: '星期一',
          2: '星期二',
          3: '星期三',
          4: '星期四',
          5: '星期五',
          6: '星期六',
          7: '星期日'
        };
        return weekDayMap[row.weekDay] || row.weekDay;
      },
    },
    {
      prop: 'timeType',
      label: '时间段类型',
      type: 'select',
      typeKey: 'select',
      props: {
        options: [
          { value: 'COURSE', label: '课表时间' },
          { value: 'FREE', label: '空闲时间' }
        ]
      },
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      formatter: (row) => {
        return row.timeType === 'COURSE' ? '课表时间' :
               row.timeType === 'FREE' ? '空闲时间' : row.timeType;
      },
    },
  ];
}
