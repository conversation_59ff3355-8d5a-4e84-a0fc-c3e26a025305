<template>
  <ele-drawer :size="530"
              :title="isUpdate ? '修改课时表 ['+data.name+']' : '添加课时表'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <pro-form ref="formRef"
              size="small"
              :model="form"
              :items="formItems"
              :grid="{ span: 24 }"
              labelWidth="auto"
              label-position="top"
              @updateValue="setFieldValue">
    </pro-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch} from 'vue';
import {ElMessage as EleMessage} from 'element-plus';
import {useFormData} from '@/utils/use-form-data.js';
import {operation} from '../api/index.js';
import ProForm from '@/components/ProForm/index.vue';
import {comColumns} from '../utils/index.js';
import {generateForm} from '@/utils/common_bak2.js';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
});

const formItems = ref([]);

/** 保存编辑 */
const save = () => {
  formRef['value']?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    let data = {...form};
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      formItems.value = comColumns() || [];
      const formInitData = generateForm(formItems.value);
      if (props.data) {
        assignFields({
          ...formInitData, ...props.data,
          weekDay: String(props.data.weekDay || '1'),
          timeType: props.data.timeType || 'COURSE', // 默认为课表时间
        });
        isUpdate.value = true;
      } else {
        resetFields({
          ...formInitData,
          timeType: 'COURSE' // 新增时默认为课表时间
        });
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef['value']?.clearValidate?.();
    }
  },
);
</script>
