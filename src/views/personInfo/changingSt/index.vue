<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <template #header>
        <!-- 搜索表单 -->
        <search @search="searchReload" ref="searchRef" />
      </template>
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :loadOnCreated="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <!-- <el-button class="ele-btn-icon" size="small" @click="openImport">
            导入
          </el-button>
          <el-button
            size="small"
            plain
            class="ele-btn-icon"
            @click="exportData()"
          >
            导出
          </el-button> -->
          <el-button size="small" plain class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 导入弹窗 -->
    <Import v-model="showImport" @done="reload" :configId="configId" />
    <!-- 批量更新 -->
    <UpdateScore
      v-model="showUpdateScore"
      :updataScoreDatas="updataScoreDatas"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { computed, nextTick, onMounted, reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage } from 'element-plus';
  import {
    getStudentRecordChangePage,
    removesStudentRecordChange
  } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { ElLoading } from 'element-plus';
  import Search from './components/search.vue';
  import Import from './components/import.vue';
  import { formMateSearchDataToStr } from '@/utils/common.js';
  import { getToken } from '@/utils/token-util';
  import { usePageTab } from '@/utils/use-page-tab.js';

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();

  let pathArray = path.split('/');
  console.log(pathArray);
  let routeType = pathArray[3];
  let userType = pathArray[4]; // 管理端：teacher  学生端：student

  /** 表格实例 */
  const tableRef = ref(null);
  /** 检索条件 */
  const searchRef = ref(null);

  /** 普通查询 -查询参数*/
  const searchWhere = reactive({});
  /** 导入弹窗 */
  const showImport = ref(false);
  /** 批量更新弹窗 */
  const showUpdateScore = ref(false);
  /** 加载状态 */
  const loading = ref(false);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    { prop: 'xgh', label: '学号' },
    { prop: 'xm', label: '姓名' },
    { prop: 'xb', label: '性别' },
    { prop: 'xymc', label: '学院' },
    { prop: 'zymc', label: '专业' },
    { prop: 'bjmc', label: '班级' },
    { prop: 'mzmc', label: '民族' },
    { prop: 'ydlb', label: '异动类别' },
    { prop: 'ydqxymc', label: '异动前学院' },
    { prop: 'ydqzymc', label: '异动前专业' },
    { prop: 'ydqbjmc', label: '异动前班级' },
    { prop: 'ydsj', label: '异动时间' },
    { prop: 'clwh', label: '处理文号' },
    { prop: 'ydsm', label: '异动明细' },
    { prop: 'czr', label: '操作人' },
    { prop: 'spzt', label: '审批状态' }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);
  const configId = ref();

  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await getStudentRecordChangePage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
    return {
      list: res.list,
      count: res.count
    };
  };

  const searchReload = (where) => {
    console.log('where :>> ', where);
    searchWhere.value = where;
    configId.value = where?.configId;
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 删除 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.xm).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removesStudentRecordChange(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /** 根据检索条件导出数据 */
  const exportData = () => {
    let where = { ...searchRef.value.initModel };
    delete where.title;
    loading.value = true;
    let searchStr = formMateSearchDataToStr(where);
    console.log('searchStr :>> ', searchStr);
    setTimeout(() => {
      window.location.href =
        BASE_URL +
        'api/evaluate/evaluate-review-score/exportData?access_token=' +
        accessToken +
        searchStr;
      loading.value = false;
    }, 3500);
  };
  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };
  const updataScoreDatas = computed(() => {
    let res = [];
    selections.value.map((item) => {
      let obj = { ...item };
      if (!obj.configId) {
        obj.configId = searchWhere.value?.configId;
      }
      res.push(obj);
    });
    return res;
  });
</script>

<script>
  export default {
    name: 'TEACHERRESULTINDEX'
  };
</script>
<style lang="scss" scoped>
  .link-text {
    color: var(--el-color-primary);
    cursor: pointer;
  }
</style>
